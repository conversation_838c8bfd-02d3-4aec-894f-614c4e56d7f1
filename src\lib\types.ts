export interface LocalizedText {
  en: string;
  ar: string;
}

export interface ServiceField {
  name: string;
  label: LocalizedText;
  type: string;
  required: boolean;
  placeholder?: LocalizedText;
  options?: {
    value: string;
    label: LocalizedText;
  }[];
}

export interface Service {
  id: string;
  title: LocalizedText;
  description: LocalizedText;
  image: string;
  price: Record<string, number | undefined>;
  currency: string;
  fields: ServiceField[];
}

export interface ServicesData {
  services: Service[];
}

export interface OrderFormData {
  serviceId: string;
  amount: string;
  playerData: Record<string, string>;
  customerInfo: {
    name: string;
    email: string;
    phone?: string;
  };
  paymentMethod: 'paypal' | 'whatsapp';
}

export type Locale = 'en' | 'ar';

export type Theme = 'light' | 'dark';
