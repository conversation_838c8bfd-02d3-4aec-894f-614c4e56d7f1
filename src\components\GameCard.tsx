import { motion } from 'framer-motion'
import { Link } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import { ArrowRight, Star, Zap } from 'lucide-react'

interface GameCardProps {
  game: {
    id: string
    name: string
    image: string
    currency: string
    minPrice: number
    popular: boolean
  }
  index: number
}

export function GameCard({ game, index }: GameCardProps) {
  const { t } = useTranslation()

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay: index * 0.1 }}
      viewport={{ once: true }}
      whileHover={{ y: -5 }}
      whileTap={{ scale: 0.98 }}
      className="game-card group relative"
    >
      {/* Popular Badge */}
      {game.popular && (
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.3 + index * 0.1 }}
          className="absolute top-3 right-3 z-10 bg-gradient-to-r from-accent-500 to-accent-600 text-white px-2 py-1 rounded-full text-xs font-semibold flex items-center shadow-lg"
        >
          <Star className="w-3 h-3 mr-1" />
          Popular
        </motion.div>
      )}

      {/* Fast Delivery Badge */}
      <motion.div
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        transition={{ delay: 0.4 + index * 0.1 }}
        className="absolute top-3 left-3 z-10 bg-green-500 text-white px-2 py-1 rounded-full text-xs font-semibold flex items-center shadow-lg"
      >
        <Zap className="w-3 h-3 mr-1" />
        Instant
      </motion.div>
      
      {/* Game Image */}
      <div className="aspect-video overflow-hidden relative">
        <img
          src={game.image}
          alt={game.name}
          className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
          loading="lazy"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
      </div>
      
      {/* Card Content */}
      <div className="p-4 sm:p-6">
        <h3 className="text-lg sm:text-xl font-semibold text-gray-900 dark:text-white mb-2 group-hover:text-gaming-600 dark:group-hover:text-gaming-400 transition-colors duration-300">
          {t(`games.${game.id}.name`)}
        </h3>
        <p className="text-sm sm:text-base text-gray-600 dark:text-gray-300 mb-4 line-clamp-2">
          {t(`games.${game.id}.description`)}
        </p>
        
        {/* Price and CTA */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-500 dark:text-gray-400">
              {t('services.from')}
            </span>
            <span className="text-lg font-bold text-gaming-600 dark:text-gaming-400">
              ${game.minPrice}
            </span>
          </div>
          
          <Link
            to={`/order/${game.id}`}
            className="inline-flex items-center justify-center touch-target px-4 py-2.5 bg-gradient-to-r from-gaming-500 to-accent-600 text-white font-medium rounded-lg hover:from-gaming-600 hover:to-accent-700 transition-all duration-300 transform hover:scale-105 text-sm shadow-lg group-hover:shadow-xl"
          >
            {t('services.buyNow')}
            <ArrowRight className="ml-2 rtl:ml-0 rtl:mr-2 rtl:rotate-180 w-4 h-4 transition-transform duration-300 group-hover:translate-x-1 rtl:group-hover:-translate-x-1" />
          </Link>
        </div>
      </div>

      {/* Hover Glow Effect */}
      <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-gaming-500/10 to-accent-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none" />
    </motion.div>
  )
}
