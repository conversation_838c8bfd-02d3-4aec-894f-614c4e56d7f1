@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-gray-200 dark:border-gray-700;
  }

  body {
    @apply bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100;
    font-feature-settings: "rlig" 1, "calt" 1;
    font-family: 'Inter', system-ui, -apple-system, sans-serif;
  }

  html {
    scroll-behavior: smooth;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-gray-100 dark:bg-gray-800;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-gray-300 dark:bg-gray-600 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400 dark:bg-gray-500;
  }
}

@layer components {
  .btn-primary {
    @apply bg-gradient-to-r from-gaming-500 to-accent-600 hover:from-gaming-600 hover:to-accent-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-gaming-500 focus:ring-offset-2 shadow-lg;
  }

  .btn-secondary {
    @apply bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100 font-medium py-3 px-6 rounded-lg transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2;
  }

  .card {
    @apply bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-6 transition-all duration-300 hover:shadow-xl;
  }

  .gaming-gradient {
    @apply bg-gradient-to-br from-gaming-500 via-gaming-600 to-accent-600;
  }

  .text-gradient {
    @apply bg-gradient-to-r from-gaming-500 to-accent-600 bg-clip-text text-transparent;
  }

  .glow-effect {
    @apply shadow-lg shadow-gaming-500/25 dark:shadow-gaming-400/25;
  }

  .game-card {
    @apply bg-white dark:bg-gray-800 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden group cursor-pointer transform hover:scale-105;
  }

  .input-field {
    @apply w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-gaming-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-all duration-200;
  }
}

@layer utilities {
  .rtl {
    direction: rtl;
  }

  .ltr {
    direction: ltr;
  }

  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  .animate-glow {
    animation: glow 2s ease-in-out infinite alternate;
  }

  .animate-bounce-slow {
    animation: bounce 2s infinite;
  }

  .animate-fade-in {
    animation: fadeIn 0.6s ease-out forwards;
  }

  .animate-slide-up {
    animation: slideUp 0.6s ease-out forwards;
  }

  @keyframes float {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-10px);
    }
  }

  @keyframes glow {
    from {
      box-shadow: 0 0 20px rgba(59, 130, 246, 0.5);
    }
    to {
      box-shadow: 0 0 30px rgba(59, 130, 246, 0.8);
    }
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes slideUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-pulse-slow {
    animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  .backdrop-blur-xs {
    backdrop-filter: blur(2px);
  }

  /* Mobile-first responsive utilities */
  .mobile-padding {
    @apply px-4 sm:px-6 lg:px-8;
  }

  .mobile-text {
    @apply text-sm sm:text-base lg:text-lg;
  }

  .mobile-heading {
    @apply text-2xl sm:text-3xl lg:text-4xl;
  }

  .mobile-hero-heading {
    @apply text-3xl sm:text-4xl md:text-5xl lg:text-6xl;
  }

  /* Touch-friendly interactive elements */
  .touch-target {
    @apply min-h-[44px] min-w-[44px];
  }

  /* Safe area for mobile devices */
  .safe-area-top {
    padding-top: env(safe-area-inset-top);
  }

  .safe-area-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }
}
