import { useState } from 'react'
import { useParams, Navigate } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import { motion } from 'framer-motion'
import { useForm } from 'react-hook-form'
import { ArrowLeft, CreditCard, MessageCircle } from 'lucide-react'

const gameData = {
  pubg: {
    name: 'PUBG Mobile',
    currency: 'UC',
    packages: [
      { amount: 60, price: 5 },
      { amount: 325, price: 25 },
      { amount: 660, price: 50 },
      { amount: 1800, price: 100 },
    ]
  },
  freefire: {
    name: 'Free Fire',
    currency: 'Diamonds',
    packages: [
      { amount: 50, price: 3 },
      { amount: 100, price: 6 },
      { amount: 310, price: 18 },
      { amount: 520, price: 30 },
    ]
  },
  mobilelegends: {
    name: 'Mobile Legends',
    currency: 'Diamonds',
    packages: [
      { amount: 86, price: 4 },
      { amount: 172, price: 8 },
      { amount: 429, price: 20 },
      { amount: 878, price: 40 },
    ]
  },
  codm: {
    name: 'Call of Duty Mobile',
    currency: 'CP',
    packages: [
      { amount: 80, price: 6 },
      { amount: 400, price: 30 },
      { amount: 800, price: 60 },
      { amount: 2000, price: 150 },
    ]
  }
}

interface OrderForm {
  gameId: string
  serverId?: string
  packageIndex: number
  email: string
  whatsapp: string
  paymentMethod: 'paypal' | 'whatsapp'
}

export function OrderPage() {
  const { gameId } = useParams<{ gameId: string }>()
  const { t } = useTranslation()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [orderSuccess, setOrderSuccess] = useState(false)

  const { register, handleSubmit, watch, formState: { errors } } = useForm<OrderForm>({
    defaultValues: {
      packageIndex: 0,
      paymentMethod: 'paypal'
    }
  })

  if (!gameId || !gameData[gameId as keyof typeof gameData]) {
    return <Navigate to="/" replace />
  }

  const game = gameData[gameId as keyof typeof gameData]
  const selectedPackageIndex = watch('packageIndex')
  const selectedPackage = game.packages[selectedPackageIndex]
  const paymentMethod = watch('paymentMethod')

  const onSubmit = async (data: OrderForm) => {
    setIsSubmitting(true)
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    setIsSubmitting(false)
    setOrderSuccess(true)
  }

  if (orderSuccess) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="max-w-md w-full mx-4 bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8 text-center"
        >
          <div className="w-16 h-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
            {t('order.success')}
          </h2>
          <p className="text-gray-600 dark:text-gray-300 mb-6">
            Your order has been placed successfully. You will receive your top-up within 5 minutes.
          </p>
          <button
            onClick={() => window.location.href = '/'}
            className="w-full btn-primary"
          >
            {t('nav.home')}
          </button>
        </motion.div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden"
        >
          {/* Header */}
          <div className="bg-gradient-to-r from-gaming-500 to-accent-600 text-white p-6">
            <div className="flex items-center space-x-4 rtl:space-x-reverse">
              <button
                onClick={() => window.history.back()}
                className="p-2 rounded-lg bg-white/20 hover:bg-white/30 transition-colors duration-200"
              >
                <ArrowLeft className="w-5 h-5" />
              </button>
              <div>
                <h1 className="text-2xl font-bold">{t('order.title')}</h1>
                <p className="text-gaming-100">{game.name}</p>
              </div>
            </div>
          </div>

          <form onSubmit={handleSubmit(onSubmit)} className="p-6 space-y-6">
            {/* Game ID */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {t('order.gameId')} *
              </label>
              <input
                {...register('gameId', { required: true })}
                type="text"
                placeholder={t('order.gameIdPlaceholder')}
                className="input-field"
              />
              {errors.gameId && (
                <p className="mt-1 text-sm text-red-600">Game ID is required</p>
              )}
            </div>

            {/* Server ID (for some games) */}
            {(gameId === 'freefire' || gameId === 'mobilelegends') && (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {t('order.serverId')}
                </label>
                <input
                  {...register('serverId')}
                  type="text"
                  placeholder={t('order.serverIdPlaceholder')}
                  className="input-field"
                />
              </div>
            )}

            {/* Package Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {t('order.package')} *
              </label>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                {game.packages.map((pkg, index) => (
                  <label
                    key={index}
                    className={`relative flex items-center justify-between p-4 border-2 rounded-lg cursor-pointer transition-all duration-200 ${
                      selectedPackageIndex === index
                        ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                        : 'border-gray-300 dark:border-gray-600 hover:border-primary-300'
                    }`}
                  >
                    <input
                      {...register('packageIndex', { required: true })}
                      type="radio"
                      value={index}
                      className="sr-only"
                    />
                    <div>
                      <div className="font-semibold text-gray-900 dark:text-white">
                        {pkg.amount} {game.currency}
                      </div>
                      <div className="text-2xl font-bold text-primary-600 dark:text-primary-400">
                        ${pkg.price}
                      </div>
                    </div>
                    {selectedPackageIndex === index && (
                      <div className="absolute top-2 right-2 w-5 h-5 bg-primary-500 rounded-full flex items-center justify-center">
                        <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      </div>
                    )}
                  </label>
                ))}
              </div>
            </div>

            {/* Contact Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {t('order.email')} *
                </label>
                <input
                  {...register('email', { required: true, pattern: /^\S+@\S+$/i })}
                  type="email"
                  placeholder={t('order.emailPlaceholder')}
                  className="input-field"
                />
                {errors.email && (
                  <p className="mt-1 text-sm text-red-600">Valid email is required</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {t('order.whatsapp')} *
                </label>
                <input
                  {...register('whatsapp', { required: true })}
                  type="tel"
                  placeholder={t('order.whatsappPlaceholder')}
                  className="input-field"
                />
                {errors.whatsapp && (
                  <p className="mt-1 text-sm text-red-600">WhatsApp number is required</p>
                )}
              </div>
            </div>

            {/* Payment Method */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {t('order.paymentMethod')} *
              </label>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <label className={`flex items-center p-4 border-2 rounded-lg cursor-pointer transition-all duration-200 ${
                  paymentMethod === 'paypal'
                    ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                    : 'border-gray-300 dark:border-gray-600 hover:border-primary-300'
                }`}>
                  <input
                    {...register('paymentMethod')}
                    type="radio"
                    value="paypal"
                    className="sr-only"
                  />
                  <CreditCard className="w-6 h-6 text-blue-600 mr-3" />
                  <span className="font-medium text-gray-900 dark:text-white">PayPal</span>
                </label>

                <label className={`flex items-center p-4 border-2 rounded-lg cursor-pointer transition-all duration-200 ${
                  paymentMethod === 'whatsapp'
                    ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                    : 'border-gray-300 dark:border-gray-600 hover:border-primary-300'
                }`}>
                  <input
                    {...register('paymentMethod')}
                    type="radio"
                    value="whatsapp"
                    className="sr-only"
                  />
                  <MessageCircle className="w-6 h-6 text-green-600 mr-3" />
                  <span className="font-medium text-gray-900 dark:text-white">WhatsApp</span>
                </label>
              </div>
            </div>

            {/* Order Summary */}
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Order Summary</h3>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-300">Game:</span>
                  <span className="font-medium text-gray-900 dark:text-white">{game.name}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-300">Package:</span>
                  <span className="font-medium text-gray-900 dark:text-white">
                    {selectedPackage.amount} {game.currency}
                  </span>
                </div>
                <div className="border-t border-gray-200 dark:border-gray-600 pt-2 mt-2">
                  <div className="flex justify-between">
                    <span className="text-lg font-semibold text-gray-900 dark:text-white">
                      {t('order.total')}:
                    </span>
                    <span className="text-lg font-bold text-primary-600 dark:text-primary-400">
                      ${selectedPackage.price}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Submit Button */}
            <button
              type="submit"
              disabled={isSubmitting}
              className="w-full bg-gradient-to-r from-gaming-500 to-accent-600 text-white font-semibold py-4 px-6 rounded-lg hover:from-gaming-600 hover:to-accent-700 transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
            >
              {isSubmitting ? t('order.processing') : t('order.placeOrder')}
            </button>
          </form>
        </motion.div>
      </div>
    </div>
  )
}
